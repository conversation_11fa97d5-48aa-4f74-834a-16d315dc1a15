"""
Business analysis functionality for KnowledgeMinerAgent.
Handles extraction of business names, descriptions, and logic from COBOL code.
"""
import re
import logging
from typing import Dict, List, Any

from langchain.schema import HumanMessage, SystemMessage
from config.constants import COBOL_ANALYST_SYSTEM_MSG, BUSINESS_NAME_EXAMPLES
from llm_settings import invoke_llm
from src.platform.tools.ims_segment_mapper import get_ims_segment_mapper


class BusinessAnalyzer:
    """
    Handles extraction of business meaning from COBOL code chunks.
    Uses LLM with templates to identify business names, descriptions, and logic.
    """

    def __init__(self, template_manager):
        """
        Initialize the business analyzer.

        Args:
            template_manager: Template manager for rendering prompts
        """

        self.template_manager = template_manager
        self.logger = logging.getLogger(__name__)
        self.ims_mapper = get_ims_segment_mapper()

    def extract_business_name(self, program_id: str, chunk_name: str,
                              code_content: str, context: str, language: str = None) -> str:
        """
        Extract business name from code using LLM.

        Args:
            program_id: ID of the program
            chunk_name: Name of the chunk
            code_content: Code content
            context: Combined context about variables and dependencies
            language: Pre-detected language (from package analyzer)

        Returns:
            str: Business name
        """
        try:
            # Use language-specific template for business name prompt
            # Use pre-detected language from package analyzer
            try:
                from src.platform.plugins.plugin_loader import get_plugin_loader

                plugin_loader = get_plugin_loader()

                # Use pre-detected language or fallback to detection if not provided
                if language:
                    detected_language = language
                    self.logger.info(f"Using pre-detected language: {detected_language} for {program_id}.{chunk_name}")
                else:
                    # Fallback to detection only if language not provided (for backward compatibility)
                    from src.platform.tools.language_detector import detect_language
                    detected_language = detect_language(code_content)
                    self.logger.warning(f"Language not provided, detected: {detected_language} for {program_id}.{chunk_name}")

                # Get the appropriate language plugin
                language_plugin = plugin_loader.get_language_plugin(detected_language)
                if language_plugin:
                    # Get IMS segment context and add to template context
                    ims_context = self.ims_mapper.get_segments_for_template_context(code_content)
                    template_context = {
                        "program_id": program_id,
                        "chunk_name": chunk_name,
                        "code_content": code_content,
                        "context": context,
                        "business_name_examples": BUSINESS_NAME_EXAMPLES
                    }
                    template_context.update(ims_context)

                    # Try to get language-specific template manager from plugin
                    business_name_prompt = self._get_language_specific_template(
                        language_plugin, detected_language, "business_name",
                        template_context
                    )
                else:
                    raise Exception(f"No plugin available for language: {detected_language}")
            except Exception as e:
                self.logger.warning(f"Plugin-based template loading failed: {e}")
                # Fallback to generic template
                business_name_prompt = f"""
                Extract a business name for the following code chunk:

                Program: {program_id}
                Chunk: {chunk_name}

                Code:
                {code_content}

                Context: {context}

                Provide a concise business name that describes what this code does.
                """

            # Use template for system message - try plugin-specific first, then fallback
            try:
                # Try to get language-specific system message from plugin
                if language_plugin:
                    system_message = self._get_language_specific_system_message(
                        language_plugin, detected_language
                    )
                else:
                    raise Exception("No language plugin available")
            except Exception:
                # Fallback to constant
                system_message = COBOL_ANALYST_SYSTEM_MSG

            name_messages = [
                SystemMessage(content=system_message),
                HumanMessage(content=business_name_prompt)
            ]

            name_response = invoke_llm(name_messages)

            # Clean up and extract just the name
            business_name = name_response.strip().strip('"')
            return business_name

        except Exception as e:
            self.logger.error(f"Error extracting business name for {program_id}.{chunk_name}: {str(e)}")
            return f"Function in {chunk_name}"

    def extract_business_description(self, program_id: str, chunk_name: str,
                                     code_content: str, context: str, language: str = None) -> str:
        """
        Extract business description from code using LLM.

        Args:
            program_id: ID of the program
            chunk_name: Name of the chunk
            code_content: Code content
            context: Combined context about variables and dependencies
            language: Pre-detected language (from package analyzer)

        Returns:
            str: Business description
        """
        try:
            # Use language-specific template for business description prompt
            try:
                from src.platform.plugins.plugin_loader import get_plugin_loader

                plugin_loader = get_plugin_loader()

                # Use pre-detected language or fallback to detection if not provided
                if language:
                    detected_language = language
                    self.logger.info(f"Using pre-detected language: {detected_language} for {program_id}.{chunk_name}")
                else:
                    # Fallback to detection only if language not provided (for backward compatibility)
                    from src.platform.tools.language_detector import detect_language
                    detected_language = detect_language(code_content)
                    self.logger.warning(f"Language not provided, detected: {detected_language} for {program_id}.{chunk_name}")

                language_plugin = plugin_loader.get_language_plugin(detected_language)

                if language_plugin:
                    # Get IMS segment context and add to template context
                    ims_context = self.ims_mapper.get_segments_for_template_context(code_content)
                    template_context = {
                        "program_id": program_id,
                        "chunk_name": chunk_name,
                        "code_content": code_content,
                        "context": context
                    }
                    template_context.update(ims_context)

                    business_desc_prompt = self._get_language_specific_template(
                        language_plugin, detected_language, "business_description",
                        template_context
                    )
                else:
                    raise Exception(f"No plugin available for language: {detected_language}")
            except Exception as e:
                self.logger.warning(f"Plugin-based template loading failed: {e}")
                # Fallback to generic template
                business_desc_prompt = f"""
                Extract a business description for the following code chunk:

                Program: {program_id}
                Chunk: {chunk_name}

                Code:
                {code_content}

                Context: {context}

                Provide a detailed business description of what this code does.
                """

            # Use template for system message - try plugin-specific first, then fallback
            try:
                # Try to get language-specific system message from plugin
                if language_plugin:
                    system_message = self._get_language_specific_system_message(
                        language_plugin, detected_language
                    )
                else:
                    raise Exception("No language plugin available")
            except Exception:
                # Fallback to constant
                system_message = COBOL_ANALYST_SYSTEM_MSG

            desc_messages = [
                SystemMessage(content=system_message),
                HumanMessage(content=business_desc_prompt)
            ]

            desc_response = invoke_llm(desc_messages)
            return desc_response.strip()

        except Exception as e:
            self.logger.error(f"Error extracting business description for {program_id}.{chunk_name}: {str(e)}")
            return f"Code chunk from {program_id}.{chunk_name}"

    def extract_business_logic(self, program_id: str, chunk_name: str,
                               code_content: str, context: str, language: str = None) -> str:
        """
        Extract business logic rules from code using LLM.

        Args:
            program_id: ID of the program
            chunk_name: Name of the chunk
            code_content: Code content
            context: Combined context about variables and dependencies
            language: Pre-detected language (from package analyzer)

        Returns:
            str: Business logic rules
        """
        try:
            # Use language-specific template for business logic prompt
            try:
                from src.platform.plugins.plugin_loader import get_plugin_loader

                plugin_loader = get_plugin_loader()

                # Use pre-detected language or fallback to detection if not provided
                if language:
                    detected_language = language
                    self.logger.info(f"Using pre-detected language: {detected_language} for {program_id}.{chunk_name}")
                else:
                    # Fallback to detection only if language not provided (for backward compatibility)
                    from src.platform.tools.language_detector import detect_language
                    detected_language = detect_language(code_content)
                    self.logger.warning(f"Language not provided, detected: {detected_language} for {program_id}.{chunk_name}")

                language_plugin = plugin_loader.get_language_plugin(detected_language)

                if language_plugin:
                    business_logic_prompt = self._get_language_specific_template(
                        language_plugin, detected_language, "business_logic",
                        {
                            "program_id": program_id,
                            "chunk_name": chunk_name,
                            "code_content": code_content,
                            "context": context
                        }
                    )
                else:
                    raise Exception(f"No plugin available for language: {detected_language}")
            except Exception as e:
                self.logger.warning(f"Plugin-based template loading failed: {e}")
                # Fallback to generic template
                business_logic_prompt = f"""
                Extract business logic rules for the following code chunk:

                Program: {program_id}
                Chunk: {chunk_name}

                Code:
                {code_content}

                Context: {context}

                Provide detailed business logic rules and processing steps.
                """

            # Use template for system message - try plugin-specific first, then fallback
            try:
                # Try to get language-specific system message from plugin
                if language_plugin:
                    system_message = self._get_language_specific_system_message(
                        language_plugin, detected_language
                    )
                else:
                    raise Exception("No language plugin available")
            except Exception:
                # Fallback to constant
                system_message = COBOL_ANALYST_SYSTEM_MSG

            logic_messages = [
                SystemMessage(content=system_message),
                HumanMessage(content=business_logic_prompt)
            ]

            logic_response = invoke_llm(logic_messages)
            return logic_response.strip()

        except Exception as e:
            self.logger.error(f"Error extracting business logic for {program_id}.{chunk_name}: {str(e)}")
            return ""

    def _get_language_specific_template(self, language_plugin, language: str, template_type: str, context: dict) -> str:
        """
        Get language-specific template content using the appropriate plugin.

        Args:
            language_plugin: The language plugin instance
            language: The detected language
            template_type: Type of template (business_name, business_description, etc.)
            context: Template context variables

        Returns:
            str: Rendered template content
        """
        try:
            # Try to get template manager from plugin (generic approach)
            if hasattr(language_plugin, 'get_template_manager'):
                template_manager = language_plugin.get_template_manager()
                template_name = f"knowledge_miner/{template_type}.j2"
                return template_manager.render_template(template_name, context)
            else:
                # Fallback: try to dynamically import language-specific template manager
                try:
                    module_path = f"src.plugins.legacy.{language}.tools.template_manager"
                    module = __import__(module_path, fromlist=[f"get_{language}_template_manager"])
                    get_template_manager_func = getattr(module, f"get_{language}_template_manager")
                    template_manager = get_template_manager_func()
                    template_name = f"knowledge_miner/{template_type}.j2"
                    return template_manager.render_template(template_name, context)
                except (ImportError, AttributeError) as import_error:
                    raise Exception(f"No template manager available for language: {language}. {import_error}")
        except Exception as e:
            self.logger.warning(f"Language-specific template loading failed for {language}: {e}")
            raise

    def _get_language_specific_system_message(self, language_plugin, language: str) -> str:
        """
        Get language-specific system message from plugin.

        Args:
            language_plugin: The language plugin instance
            language: Language name

        Returns:
            str: System message content
        """
        try:
            # Try to get template manager from plugin (generic approach)
            if hasattr(language_plugin, 'get_template_manager'):
                template_manager = language_plugin.get_template_manager()
                template_name = "system_messages/cobol_analyst.j2"
                return template_manager.render_template(template_name, {})
            else:
                # Fallback: try to dynamically import language-specific template manager
                try:
                    module_path = f"src.plugins.legacy.{language}.tools.template_manager"
                    module = __import__(module_path, fromlist=[f"get_{language}_template_manager"])
                    get_template_manager_func = getattr(module, f"get_{language}_template_manager")
                    template_manager = get_template_manager_func()
                    template_name = "system_messages/cobol_analyst.j2"
                    return template_manager.render_template(template_name, {})
                except Exception as e:
                    self.logger.warning(f"Could not load language-specific template manager for {language}: {e}")
                    raise e
        except Exception as e:
            self.logger.error(f"Error loading system message for language {language}: {e}")
            raise e

    def extract_business_logic_with_aggregation(self, program_id: str, chunk_name: str,
                                                code_content: str, context: str,
                                                referenced_chunks_analysis: List[Dict[str, Any]],
                                                language: str = None) -> str:
        """
        Extract business logic rules with aggregation from called procedures.

        Args:
            program_id: ID of the program
            chunk_name: Name of the chunk
            code_content: Code content
            context: Combined context about variables and dependencies
            referenced_chunks_analysis: List of analysis results for called procedures
            language: Pre-detected language (from package analyzer)

        Returns:
            str: Business logic rules including aggregated rules
        """
        # Extract base business logic
        base_logic = self.extract_business_logic(program_id, chunk_name, code_content, context, language)

        # Get aggregated business rules from called procedures
        aggregated_rules = self._aggregate_business_rules(referenced_chunks_analysis)

        # Combine base logic with aggregated rules
        if base_logic and aggregated_rules:
            return f"{base_logic}\n\n{aggregated_rules}"
        elif base_logic:
            return base_logic
        elif aggregated_rules:
            return aggregated_rules
        else:
            return ""

    def _aggregate_business_rules(self, referenced_chunks_analysis: List[Dict[str, Any]]) -> str:
        """
        Aggregate business rules from called procedures, filtering out technical rules.

        Args:
            referenced_chunks_analysis: List of analysis results for called procedures

        Returns:
            str: Aggregated business rules context
        """
        if not referenced_chunks_analysis:
            return ""

        business_rules = []

        for analysis in referenced_chunks_analysis:
            logic = analysis.get('business_logic', '')
            if logic and logic != "No explicit business rules found.":
                # Filter out technical rules (basic heuristics)
                rules = self._filter_technical_rules(logic)
                if rules:
                    proc_name = analysis.get('name', '').split('_PROC_')[-1] if analysis.get('name') else 'Unknown'
                    business_rules.append(f"From {proc_name}: {rules}")

        if business_rules:
            return "Business rules from called procedures:\n" + "\n".join(business_rules)
        return ""

    def _filter_technical_rules(self, business_logic: str) -> str:
        """
        Filter out technical rules from business logic text.
        This is a heuristic approach - in a real implementation you might want to use NLP.

        Args:
            business_logic: Raw business logic text

        Returns:
            str: Filtered business logic (technical rules removed)
        """
        # Technical patterns to filter out
        technical_patterns = [
            r'.*file.*open.*error.*',
            r'.*return.*code.*',
            r'.*error.*handling.*',
            r'.*eof.*check.*',
            r'.*status.*validation.*',
            r'.*flag.*set.*',
            r'.*counter.*increment.*',
            r'.*exit.*condition.*',
            r'.*initialization.*routine.*',
            r'.*cleanup.*process.*'
        ]

        lines = business_logic.split('\n')
        filtered_lines = []

        for line in lines:
            line_lower = line.lower()
            is_technical = False

            for pattern in technical_patterns:
                if re.match(pattern, line_lower):
                    is_technical = True
                    break

            if not is_technical:
                filtered_lines.append(line)

        filtered_logic = '\n'.join(filtered_lines).strip()

        # If we filtered out everything, return a placeholder
        if not filtered_logic:
            return "No explicit business rules found."

        return filtered_logic
