You are an expert COBOL code analyst.
Analyze the following COBOL code chunk from program {{ program_id }}, chunk {{ chunk_name }}:

```cobol
{{ code_content }}
```

{{ context }}

{% if has_ims_segments %}
**IMS SEGMENT BUSINESS CONTEXT:**
The following IMS database segments are referenced in this code with their business meanings:
{% for segment, business_name in segment_business_mappings.items() %}
- **{{ segment }}**: {{ business_name }}
{% endfor %}

**IMPORTANT:** When providing business_name and description for IMS segments, use the business names above. For example:
- AMSAM0A should be described as "Card Number Information" not "Account Sub-Segment"
- CUSCM01 should be described as "Customer Address Information" not "Customer Segment"
- RWSRW00 should be described as "Rewards Information" not "Rewards Segment"
{% endif %}

IDENTIFY ALL OUTPUT RESOURCES produced or modified by this code. Consider all possible outputs including:
- Variables that are modified/written to
- Records that are read in this code chunk from opened files or db tables (like READ ACCTFILE-FILE INTO ACCOUNT-RECORD.)
- Flat Files, VSAM, etc, that are written to (OPEN OUTPUT, WRITE)
- Database tables of DB2, IMS DB, etc, that are updated (INSERT, UPDATE, DELETE)
- Message queues that are written to
- APIs or external systems that receive data

IMPORTANT: Records that are read in this code chunk from opened files or db tables (like READ ACCTFILE-FILE INTO ACCOUNT-RECORD.) - are output parameters, not input

For each output resource, provide the following details:
- name: The exact name as it appears in code
- type: The type of resource (variable, file, VSAM, database db2, ims db, queue, api, report, etc.)
- business_name: A clear business-oriented name
- description: A brief description of what this output represents

Format your response as a JSON array of objects like this:
[
  {
    "name": "CUSTOMER-BALANCE",
    "type": "variable",
    "business_name": "Customer Account Balance",
    "description": "The updated account balance after transaction processing"
  },
  {
    "name": "TRANSACTION-LOG",
    "type": "VSAM file",
    "business_name": "Transaction Log File",
    "description": "Record of all transactions processed in this run"
  }
]

If there are no outputs, return an empty array: []
