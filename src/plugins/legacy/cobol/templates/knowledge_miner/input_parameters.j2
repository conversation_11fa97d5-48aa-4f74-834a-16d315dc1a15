You are an expert COBOL code analyst.
Analyze the following COBOL code chunk from program {{ program_id }}, chunk {{ chunk_name }}:

```cobol
{{ code_content }}
```

{{ context }}

{% if has_ims_segments %}
**IMS SEGMENT BUSINESS CONTEXT:**
The following IMS database segments are referenced in this code with their business meanings:
{% for segment, business_name in segment_business_mappings.items() %}
- **{{ segment }}**: {{ business_name }}
{% endfor %}

**IMPORTANT:** When providing business_name and description for IMS segments, use the business names above. For example:
- AMSAM0A should be described as "Card Number Information" not "Account Sub-Segment"
- CUSCM01 should be described as "Customer Address Information" not "Customer Segment"
- RWSRW00 should be described as "Rewards Information" not "Rewards Segment"
{% endif %}

IDENTIFY ALL INPUT RESOURCES used by this code. Consider all possible inputs including:
- Variables that we are read from
- Flat Files, VSAM, etc, that are read from (OPEN INPUT, READ)
- Database tables on IBM DB2, IMS DB, etc, that are queried (SELECT)
- Message queues that are read from
- APIs or external systems that provide data
- Environment variables or configuration settings

For each input resource, provide the following details:
- name: The exact name as it appears in code
- type: The type of resource (variable, flat file, VSAM, IBM DB2 database, IMS DB, queue, api, etc.)
- business_name: A clear business-oriented name
- description: A brief description of what this input represents

IMPORTANT: Records that are read in this code chunk from opened files or db tables (like READ ACCTFILE-FILE INTO ACCOUNT-RECORD.) - are output parameters, not input.But related files and tables - are input parameters!

Format your response as a JSON array of objects like this:
[
  {
    "name": "CUSTOMER-ID",
    "type": "variable",
    "business_name": "Customer Identifier",
    "description": "Unique identifier for the customer record"
  },
  {
    "name": "SALES-HISTORY",
    "type": "VSAM",
    "business_name": "Sales History File",
    "description": "Contains historical sales records for analysis"
  }
]

If there are no inputs, return an empty array: []
