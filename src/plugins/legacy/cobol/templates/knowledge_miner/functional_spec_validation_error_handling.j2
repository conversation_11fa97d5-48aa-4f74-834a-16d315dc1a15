** ROLE **

You are a functional specification analysis expert specializing in validation and error handling documentation.

** TASK **

Analyze the following functional specification for program {{ program_id }}, chunk {{ chunk_name }}:

```
{{ functional_spec }}
```

{{ context }}

{% if has_ims_segments %}
**IMS SEGMENT BUSINESS CONTEXT:**
The following IMS database segments are referenced in this code with their business meanings:
{% for segment, business_name in segment_business_mappings.items() %}
- **{{ segment }}**: {{ business_name }}
{% endfor %}

**IMPORTANT:** When documenting validation rules and error handling, use the business names above. For example:
- AMSAM0A should be referred to as "Card Number Information" not "Account Sub-Segment"
- CUSCM01 should be referred to as "Customer Address Information" not "Customer Segment"
- RWSRW00 should be referred to as "Rewards Information" not "Rewards Segment"
{% endif %}

Analyze the provided functional specification and create two structured tables: Validation Rules and Error Handling.
These tables should document all validation logic, error conditions, and error handling procedures described in the specification.

** Required Output Format **

Create two markdown tables with the following structure:

**Table 1: Validation Rules**

| ID | Field/Data | Check Method | Error Condition | Error Handler |
|----|------------|--------------|-----------------|---------------|
| V1 | [field name] | [validation method] | [error condition] | [error handler reference] |

**Table 2: Error Handling**

| ID | Field/Data | Error Condition | Error Response | Fallback Action |
|----|------------|-----------------|----------------|-----------------|
| E1 | [field name] | [error condition] | [response actions] | [fallback process] |

**Column Descriptions:**
- ID: Sequential identifier (V1, V2, V3, etc. for validation rules; E1, E2, E3, etc. for error handling)
- Field/Data: The business data element or parameter being validated/handled
- Check Method: The specific validation method or business rule used
- Error Condition: What business condition triggers an error
- Error Handler: Reference to the error handling procedure or n/a if no error is produced
- Error Response: What business actions are taken when the error occurs
- Fallback Action: What business process happens after error processing

** Analysis Instructions **

** Step 1: Identify Business Validation Logic **
Focus ONLY on validations described in the functional specification that enforce:
- Data quality rules (required fields, format checks)
- Business rules (eligibility, limits, constraints)
- Data integrity (consistency between related fields)
- External data validation (file content, transaction validity)

EXCLUDE simple control flow logic like:
- Basic conditional branching for process navigation
- Loop counters and iteration control
- Simple status flag checks
- Routine data processing controls

** Step 2: Extract Validation Rules **
For each validation described in the specification:

- Identify the business data element being validated
- Document the exact validation method or business rule
- Specify what business condition constitutes an error
- Link to the corresponding error handling procedure

** Step 3: Identify Error Handling Procedures **
Look for these error handling patterns described in the specification:

- Error flag establishment and management
- Error message population and handling
- Error processing procedure execution
- Conditional logic that handles different error scenarios
- Process continuation after errors

** Step 4: Document Error Responses **
For each error handler described in the specification:

- Identify what business condition triggers the error handling
- List all business actions taken during error processing
- Document any fallback actions or process continuation logic

** Rules **
Rule 1: Be Specific: Use exact business process names and parameter names from the specification
Rule 2: Be Complete: Capture all validation and error handling logic described in the specification
Rule 3: Be Accurate: Ensure error handlers correctly correspond to validation rules
Rule 4: Be Consistent: Use consistent terminology and formatting throughout

** Expected Deliverable **
Provide two well-structured tables that completely document:

- All validation rules described in the functional specification
- All error handling procedures and their business responses
- Clear relationships between validation failures and error handling procedures
- Specific business processes and parameter names from the specification

** Self-Check **
Before submitting, verify each validation rule has a corresponding error handler.

** Output rule **
Provide ONLY the two markdown tables as specified above. Do not include any explanatory text, thinking process, or additional commentary. Start directly with "**Table 1: Validation Rules**" and end with the last row of Table 2.

IMPORTANT: Do not wrap your response in markdown code blocks (```markdown or ```). Provide the tables directly as plain markdown text.
